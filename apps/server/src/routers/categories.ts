import { TRPCError } from "@trpc/server";
import { Category } from "../db/models/financial.model";
import { protectedProcedure, router } from "../lib/trpc";
import {
  createCategorySchema,
  updateCategorySchema,
  categoryIdSchema,
} from "../lib/schemas";

export const categoriesRouter = router({
  // Get all categories for the authenticated user
  getAll: protectedProcedure.query(async ({ ctx }) => {
    try {
      const categories = await Category.find({
        $or: [{ userId: ctx.session.user.id }, { isDefault: true }],
      }).sort({ type: 1, name: 1 });

      return categories;
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch categories",
      });
    }
  }),

  // Get categories by type (income or expense)
  getByType: protectedProcedure
    .input(createCategorySchema.pick({ type: true }))
    .query(async ({ ctx, input }) => {
      try {
        const categories = await Category.find({
          $or: [
            { userId: ctx.session.user.id, type: input.type },
            { isDefault: true, type: input.type },
          ],
        }).sort({ name: 1 });

        return categories;
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch categories",
        });
      }
    }),

  // Create a new category
  create: protectedProcedure
    .input(createCategorySchema)
    .mutation(async ({ ctx, input }) => {
      try {
        // Check if category with same name already exists for this user
        const existingCategory = await Category.findOne({
          userId: ctx.session.user.id,
          name: input.name,
          type: input.type,
        });

        if (existingCategory) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "Category with this name already exists",
          });
        }

        const category = new Category({
          ...input,
          userId: ctx.session.user.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        await category.save();
        return category;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create category",
        });
      }
    }),

  // Update an existing category
  update: protectedProcedure
    .input(categoryIdSchema.merge(updateCategorySchema))
    .mutation(async ({ ctx, input }) => {
      try {
        const { id, ...updateData } = input;

        const category = await Category.findOne({
          _id: id,
          userId: ctx.session.user.id,
        });

        if (!category) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Category not found",
          });
        }

        // Check if updating name would create a duplicate
        if (updateData.name) {
          const existingCategory = await Category.findOne({
            userId: ctx.session.user.id,
            name: updateData.name,
            type: updateData.type || category.type,
            _id: { $ne: id },
          });

          if (existingCategory) {
            throw new TRPCError({
              code: "CONFLICT",
              message: "Category with this name already exists",
            });
          }
        }

        Object.assign(category, updateData);
        category.updatedAt = new Date();

        await category.save();
        return category;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update category",
        });
      }
    }),

  // Delete a category
  delete: protectedProcedure
    .input(categoryIdSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const category = await Category.findOne({
          _id: input.id,
          userId: ctx.session.user.id,
        });

        if (!category) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Category not found",
          });
        }

        // Check if category is being used by any transactions
        const { Transaction } = await import("../db/models/financial.model");
        const transactionCount = await Transaction.countDocuments({
          categoryId: input.id,
          userId: ctx.session.user.id,
        });

        if (transactionCount > 0) {
          throw new TRPCError({
            code: "PRECONDITION_FAILED",
            message: "Cannot delete category that has associated transactions",
          });
        }

        await Category.deleteOne({ _id: input.id });
        return { success: true };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete category",
        });
      }
    }),

  // Get a single category by ID
  getById: protectedProcedure
    .input(categoryIdSchema)
    .query(async ({ ctx, input }) => {
      try {
        const category = await Category.findOne({
          _id: input.id,
          $or: [{ userId: ctx.session.user.id }, { isDefault: true }],
        });

        if (!category) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Category not found",
          });
        }

        return category;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch category",
        });
      }
    }),
});
