import { protectedProcedure, publicProcedure, router } from "../lib/trpc";
import { categoriesRouter } from "./categories";
import { transactionsRouter } from "./transactions";
import { budgetsRouter } from "./budgets";
import { analyticsRouter } from "./analytics";

export const appRouter = router({
  healthCheck: publicProcedure.query(() => {
    return "OK";
  }),
  privateData: protectedProcedure.query(({ ctx }) => {
    return {
      message: "This is private",
      user: ctx.session.user,
    };
  }),

  // Financial management routers
  categories: categoriesRouter,
  transactions: transactionsRouter,
  budgets: budgetsRouter,
  analytics: analyticsRouter,
});

export type AppRouter = typeof appRouter;
