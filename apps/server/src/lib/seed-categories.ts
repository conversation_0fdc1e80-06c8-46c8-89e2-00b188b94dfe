import { Category } from "../db/models/financial.model";

// Default categories that will be available to all users
const defaultCategories = [
  // Income categories
  {
    name: "Salary",
    type: "income",
    color: "#10B981",
    icon: "Briefcase",
    isDefault: true,
  },
  {
    name: "Freelance",
    type: "income",
    color: "#8B5CF6",
    icon: "Laptop",
    isDefault: true,
  },
  {
    name: "Investment",
    type: "income",
    color: "#F59E0B",
    icon: "TrendingUp",
    isDefault: true,
  },
  {
    name: "Gift",
    type: "income",
    color: "#EF4444",
    icon: "Gift",
    isDefault: true,
  },
  {
    name: "Other Income",
    type: "income",
    color: "#6B7280",
    icon: "Plus",
    isDefault: true,
  },

  // Expense categories
  {
    name: "Food & Dining",
    type: "expense",
    color: "#EF4444",
    icon: "UtensilsCrossed",
    isDefault: true,
  },
  {
    name: "Transportation",
    type: "expense",
    color: "#3B82F6",
    icon: "Car",
    isDefault: true,
  },
  {
    name: "Shopping",
    type: "expense",
    color: "#EC4899",
    icon: "ShoppingBag",
    isDefault: true,
  },
  {
    name: "Entertainment",
    type: "expense",
    color: "#8B5CF6",
    icon: "Film",
    isDefault: true,
  },
  {
    name: "Bills & Utilities",
    type: "expense",
    color: "#F59E0B",
    icon: "Receipt",
    isDefault: true,
  },
  {
    name: "Healthcare",
    type: "expense",
    color: "#10B981",
    icon: "Heart",
    isDefault: true,
  },
  {
    name: "Education",
    type: "expense",
    color: "#06B6D4",
    icon: "GraduationCap",
    isDefault: true,
  },
  {
    name: "Travel",
    type: "expense",
    color: "#84CC16",
    icon: "Plane",
    isDefault: true,
  },
  {
    name: "Insurance",
    type: "expense",
    color: "#6366F1",
    icon: "Shield",
    isDefault: true,
  },
  {
    name: "Savings",
    type: "expense",
    color: "#059669",
    icon: "PiggyBank",
    isDefault: true,
  },
  {
    name: "Other Expenses",
    type: "expense",
    color: "#6B7280",
    icon: "Minus",
    isDefault: true,
  },
];

/**
 * Seeds default categories into the database
 * This should be called when the application starts
 */
export async function seedDefaultCategories() {
  try {
    // Check if default categories already exist
    const existingDefaults = await Category.countDocuments({ isDefault: true });

    if (existingDefaults > 0) {
      console.log("Default categories already exist, skipping seed");
      return;
    }

    // Create default categories
    const categories = defaultCategories.map((cat) => ({
      ...cat,
      userId: "system", // Special user ID for system categories
      createdAt: new Date(),
      updatedAt: new Date(),
    }));

    await Category.insertMany(categories);
    console.log(`Successfully seeded ${categories.length} default categories`);
  } catch (error) {
    console.error("Error seeding default categories:", error);
  }
}

/**
 * Creates user-specific copies of default categories when a user signs up
 * This ensures each user has their own set of categories they can modify
 */
export async function createUserCategories(userId: string) {
  try {
    // Check if user already has categories
    const existingUserCategories = await Category.countDocuments({ userId });

    if (existingUserCategories > 0) {
      console.log(`User ${userId} already has categories, skipping creation`);
      return;
    }

    // Get default categories
    const defaultCats = await Category.find({ isDefault: true });

    // Create user-specific copies
    const userCategories = defaultCats.map((cat) => ({
      name: cat.name,
      type: cat.type,
      color: cat.color,
      icon: cat.icon,
      userId: userId,
      isDefault: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    }));

    await Category.insertMany(userCategories);
    console.log(
      `Successfully created ${userCategories.length} categories for user ${userId}`
    );
  } catch (error) {
    console.error(`Error creating categories for user ${userId}:`, error);
  }
}
