import { create<PERSON>ile<PERSON><PERSON><PERSON>, <PERSON> } from "@tanstack/react-router";
import { authClient } from "@/lib/auth-client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DollarSign,
  TrendingUp,
  PieChart,
  Shield,
  Smartphone,
  BarChart3,
  Target,
  CreditCard,
  Wallet,
  ArrowRight,
  CheckCircle,
  Star
} from "lucide-react";

export const Route = createFileRoute("/")({
  component: HomeComponent,
});

function HomeComponent() {
  const { data: session } = authClient.useSession();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section */}
      <section className="relative overflow-hidden px-4 py-20 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="text-center">
            <Badge variant="secondary" className="mb-4 px-3 py-1">
              <Star className="mr-1 h-3 w-3" />
              Trusted by thousands of users
            </Badge>

            <h1 className="mx-auto max-w-4xl text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-6xl">
              Take Control of Your{" "}
              <span className="bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                Financial Future
              </span>
            </h1>

            <p className="mx-auto mt-6 max-w-2xl text-lg leading-8 text-gray-600 dark:text-gray-300">
              Track expenses, manage budgets, and gain insights into your spending habits with our
              intuitive money management platform. Make informed financial decisions with real-time analytics.
            </p>

            <div className="mt-10 flex items-center justify-center gap-x-6">
              {session ? (
                <Button asChild size="lg" className="px-8 py-3">
                  <Link to="/dashboard">
                    Go to Dashboard
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              ) : (
                <Button asChild size="lg" className="px-8 py-3">
                  <Link to="/login">
                    Get Started Free
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              )}

              <Button variant="outline" size="lg" className="px-8 py-3">
                Learn More
              </Button>
            </div>
          </div>
        </div>

        {/* Background decoration */}
        <div className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]">
          <div className="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-blue-400 to-green-400 opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]" />
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
              Everything you need to manage your money
            </h2>
            <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
              Powerful features designed to help you take control of your finances
            </p>
          </div>

          <div className="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {/* Feature 1 */}
            <Card className="relative overflow-hidden border-0 bg-white/50 backdrop-blur-sm dark:bg-gray-800/50">
              <CardHeader>
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900">
                  <Wallet className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <CardTitle className="text-xl">Expense Tracking</CardTitle>
                <CardDescription>
                  Automatically categorize and track all your expenses with intelligent insights
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Feature 2 */}
            <Card className="relative overflow-hidden border-0 bg-white/50 backdrop-blur-sm dark:bg-gray-800/50">
              <CardHeader>
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900">
                  <Target className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
                <CardTitle className="text-xl">Budget Management</CardTitle>
                <CardDescription>
                  Set budgets, track progress, and get alerts when you're approaching limits
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Feature 3 */}
            <Card className="relative overflow-hidden border-0 bg-white/50 backdrop-blur-sm dark:bg-gray-800/50">
              <CardHeader>
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900">
                  <BarChart3 className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
                <CardTitle className="text-xl">Financial Analytics</CardTitle>
                <CardDescription>
                  Visualize your spending patterns with interactive charts and detailed reports
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Feature 4 */}
            <Card className="relative overflow-hidden border-0 bg-white/50 backdrop-blur-sm dark:bg-gray-800/50">
              <CardHeader>
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100 dark:bg-orange-900">
                  <TrendingUp className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                </div>
                <CardTitle className="text-xl">Income Tracking</CardTitle>
                <CardDescription>
                  Monitor multiple income sources and track your financial growth over time
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Feature 5 */}
            <Card className="relative overflow-hidden border-0 bg-white/50 backdrop-blur-sm dark:bg-gray-800/50">
              <CardHeader>
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-red-100 dark:bg-red-900">
                  <Shield className="h-6 w-6 text-red-600 dark:text-red-400" />
                </div>
                <CardTitle className="text-xl">Secure & Private</CardTitle>
                <CardDescription>
                  Bank-level security with end-to-end encryption to keep your data safe
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Feature 6 */}
            <Card className="relative overflow-hidden border-0 bg-white/50 backdrop-blur-sm dark:bg-gray-800/50">
              <CardHeader>
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-cyan-100 dark:bg-cyan-900">
                  <Smartphone className="h-6 w-6 text-cyan-600 dark:text-cyan-400" />
                </div>
                <CardTitle className="text-xl">Mobile Responsive</CardTitle>
                <CardDescription>
                  Access your finances anywhere with our fully responsive web application
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="bg-gray-50 dark:bg-gray-900 py-20 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-1 gap-16 lg:grid-cols-2 lg:gap-24">
            <div>
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
                Why choose Money Manager?
              </h2>
              <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
                Join thousands of users who have transformed their financial habits with our platform.
              </p>

              <div className="mt-8 space-y-6">
                <div className="flex items-start">
                  <CheckCircle className="mt-1 h-5 w-5 text-green-500 flex-shrink-0" />
                  <div className="ml-3">
                    <h3 className="font-semibold text-gray-900 dark:text-white">Easy to Use</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Intuitive interface designed for users of all experience levels
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <CheckCircle className="mt-1 h-5 w-5 text-green-500 flex-shrink-0" />
                  <div className="ml-3">
                    <h3 className="font-semibold text-gray-900 dark:text-white">Real-time Insights</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Get instant feedback on your spending and saving patterns
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <CheckCircle className="mt-1 h-5 w-5 text-green-500 flex-shrink-0" />
                  <div className="ml-3">
                    <h3 className="font-semibold text-gray-900 dark:text-white">Completely Free</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Full access to all features without any hidden costs or subscriptions
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="aspect-square rounded-2xl bg-gradient-to-br from-blue-100 to-green-100 dark:from-blue-900 dark:to-green-900 p-8">
                <div className="grid grid-cols-2 gap-4 h-full">
                  <Card className="flex items-center justify-center">
                    <CardContent className="p-4 text-center">
                      <DollarSign className="h-8 w-8 mx-auto mb-2 text-green-600" />
                      <p className="text-sm font-medium">Track Expenses</p>
                    </CardContent>
                  </Card>
                  <Card className="flex items-center justify-center">
                    <CardContent className="p-4 text-center">
                      <PieChart className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                      <p className="text-sm font-medium">View Analytics</p>
                    </CardContent>
                  </Card>
                  <Card className="flex items-center justify-center">
                    <CardContent className="p-4 text-center">
                      <Target className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                      <p className="text-sm font-medium">Set Budgets</p>
                    </CardContent>
                  </Card>
                  <Card className="flex items-center justify-center">
                    <CardContent className="p-4 text-center">
                      <TrendingUp className="h-8 w-8 mx-auto mb-2 text-orange-600" />
                      <p className="text-sm font-medium">Monitor Growth</p>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
            Ready to take control of your finances?
          </h2>
          <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
            Start your journey to financial freedom today. No credit card required.
          </p>

          <div className="mt-8 flex items-center justify-center gap-x-6">
            {session ? (
              <Button asChild size="lg" className="px-8 py-3">
                <Link to="/dashboard">
                  Open Dashboard
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            ) : (
              <Button asChild size="lg" className="px-8 py-3">
                <Link to="/login">
                  Start Free Today
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            )}
          </div>

          <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">
            Join thousands of users already managing their finances smarter
          </p>
        </div>
      </section>
    </div>
  );
}
